<?xml version="1.0" ?>
<!--This does not replace URDF, and is not an extension of URDF.
    This is a format for representing semantic information about the robot structure.
    A URDF file must exist for this robot as well, where the joints and the links that are referenced are defined
-->
<robot name="cr10_robot">
    <!--GROUPS: Representation of a set of joints and links. This can be useful for specifying DOF to plan for, defining arms, end effectors, etc-->
    <!--LINKS: When a link is specified, the parent joint of that link (if it exists) is automatically included-->
    <!--JOINTS: When a joint is specified, the child link of that joint (which will always exist) is automatically included-->
    <!--CHAINS: When a chain is specified, all the links along the chain (including endpoints) are included in the group. Additionally, all the joints that are parents to included links are also included. This means that joints along the chain and the parent joint of the base link are included in the group-->
    <!--SUBGROUPS: Groups can also be formed by referencing to already defined group names-->
    <group name="arm">
        <joint name="joint6" />
        <joint name="joint1" />
        <joint name="joint2" />
        <joint name="joint3" />
        <joint name="joint4" />
        <joint name="joint5" />
    </group>
    <!--GROUP STATES: Purpose: Define a named state for a particular group, in terms of joint values. This is useful to define states like 'folded arms'-->
    <group_state name="home" group="arm">
        <joint name="joint1" value="0" />
        <joint name="joint2" value="0" />
        <joint name="joint3" value="0" />
        <joint name="joint4" value="0" />
        <joint name="joint5" value="0" />
        <joint name="joint6" value="0" />
    </group_state>
    <!--VIRTUAL JOINT: Purpose: this element defines a virtual joint between a robot link and an external frame of reference (considered fixed with respect to the robot)-->
    <virtual_joint name="virtual_joint" type="fixed" parent_frame="world" child_link="dummy_link" />
    <!--DISABLE COLLISIONS: By default it is assumed that any link of the robot could potentially come into collision with any other link in the robot. This tag disables collision checking between a specified pair of links. -->
    <disable_collisions link1="Link1" link2="Link2" reason="Adjacent" />
    <disable_collisions link1="Link1" link2="Link3" reason="Never" />
    <disable_collisions link1="Link1" link2="Link4" reason="Never" />
    <disable_collisions link1="Link1" link2="base_link" reason="Adjacent" />
    <disable_collisions link1="Link2" link2="Link3" reason="Adjacent" />
    <disable_collisions link1="Link3" link2="Link4" reason="Adjacent" />
    <disable_collisions link1="Link4" link2="Link5" reason="Adjacent" />
    <disable_collisions link1="Link4" link2="Link6" reason="Never" />
    <disable_collisions link1="Link5" link2="Link6" reason="Adjacent" />
</robot>
