<launch>

  <!-- This argument must specify the list of .cfg files to process for benchmarking -->
  <arg name="cfg" />

  <!-- Load URDF -->
  <include file="$(find cr10_moveit)/launch/planning_context.launch">
    <arg name="load_robot_description" value="true"/>
  </include>

  <!-- Start the database -->
  <include file="$(find cr10_moveit)/launch/warehouse.launch">
    <arg name="moveit_warehouse_database_path" value="moveit_ompl_benchmark_warehouse"/>
  </include>

  <!-- Start Benchmark Executable -->
  <node name="$(anon moveit_benchmark)" pkg="moveit_ros_benchmarks" type="moveit_run_benchmark" args="$(arg cfg) --benchmark-planners" respawn="false" output="screen">
    <rosparam command="load" file="$(find cr10_moveit)/config/ompl_planning.yaml"/>
  </node>

</launch>
